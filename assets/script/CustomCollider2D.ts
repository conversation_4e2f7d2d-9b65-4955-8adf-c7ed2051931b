import {
  _decorator,
  CCBoolean,
  CCInteger,
  Component,
  PolygonCollider2D,
  RigidBody2D,
  Vec2,
} from 'cc';
const { ccclass, property, requireComponent } = _decorator;

const GAP_EACH_RING = 0.5; // in px

@ccclass('CustomCollider2D')
@requireComponent(PolygonCollider2D)
@requireComponent(RigidBody2D)
export class CustomCollider2D extends Component {
  @property(CCBoolean)
  private inverseCollider = false;

  @property({
    type: CCInteger,
    visible: function (this: CustomCollider2D) {
      return this.inverseCollider;
    },
  })
  private thickness = 1;

  private polygonCollider: PolygonCollider2D | null = null;
  private rigidBody: RigidBody2D | null = null;

  onLoad() {
    this.updateCollider();
  }

  public updateCollider(): PolygonCollider2D | null {
    this.polygonCollider = this.getComponent(PolygonCollider2D);
    this.rigidBody = this.getComponent(RigidBody2D);

    // TODO in the future: Currently this solution is only available for convex polygon
    // For concave polygon please do a research or use clipper-lib library if possible.
    if (this.inverseCollider && this.polygonCollider) {
      const innerPoints = [...this.polygonCollider.points];

      const outerPoints = this.polygonCollider.points.map((point) => {
        const signX = point.x / Math.abs(point.x);
        const signY = point.y / Math.abs(point.y);

        return new Vec2(
          signX * (Math.abs(point.x) + this.thickness),
          signY * (Math.abs(point.y) + this.thickness),
        );
      });

      const innerTrfPoint = new Vec2(innerPoints[0].x + GAP_EACH_RING, innerPoints[0].y + GAP_EACH_RING);
      const outerTrfPoint = new Vec2(outerPoints[0].x + GAP_EACH_RING, outerPoints[0].y + GAP_EACH_RING);
      const outerLastPoint = outerPoints[0];
      const outerPointsCCW = [...outerPoints.slice(1).reverse()];

      const newPoints = [
        ...innerPoints,
        innerTrfPoint,
        outerTrfPoint,
        ...outerPointsCCW,
        outerLastPoint,
      ];

      this.polygonCollider.points = newPoints;

      this.polygonCollider.apply();
    }

    return this.polygonCollider;
  }
}
